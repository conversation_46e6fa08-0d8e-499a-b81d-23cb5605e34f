<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-10 08:57:31
 * @LastEditors: wjb
 * @LastEditTime: 2025-07-17 08:57:52
-->
<template>
  <div>
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="告警状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form> -->
    <el-table :data="datalist">
      <el-table-column prop="deptName" label="关联部门" align="center" />
      <el-table-column prop="yyName" label="关联应用" align="center" />
      <el-table-column prop="gjsj" label="告警时间" align="center" />
      <el-table-column prop="status" label="告警状态" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.status == 1
                  ? 'tag_red'
                  : scope.row.status == 2
                  ? 'tag_blu'
                  : ''
              "
            >
              {{ scope.row.status == 1 ? "处理中" : "已处理" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="xtStatus" label="系统状态" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.xtStatus == 1">谋划中</div>
          <div v-if="scope.row.xtStatus == 2">建设中</div>
          <div v-if="scope.row.xtStatus == 3">试运行</div>
          <div v-if="scope.row.xtStatus == 4">运行中</div>
          <div v-if="scope.row.xtStatus == 5">停用</div>
        </template></el-table-column
      >
      <el-table-column prop="adminName" label="应用管理员" align="center" />
      <el-table-column prop="ywcs" label="运维厂商" align="center" />
      <el-table-column prop="" label="处置方式" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.czfs == 1">忽略</div>
          <div v-if="scope.row.czfs == 2">转工单</div>
        </template></el-table-column
      >
      <el-table-column label="操作" align="center" width="220">
        <template slot-scope="scope">
          <el-button type="text" @click="showBreakpointDialog(scope.row)"
            >监测日志</el-button
          >
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            v-if="!scope.row.czfs && !scope.row.gdId"
            @click="goHide(scope.row)"
            >忽略</el-button
          >
          <el-button type="text" v-else disabled>忽略</el-button>
          <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          >
          <el-button type="text" disabled v-else>转工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <infoDialog :show="show" :info="info" @close="show = false"></infoDialog>

    <!-- 转工单弹窗 -->
    <addOrderDialog
      :show="show1"
      :info="info1"
      :deptName1="info1DeptName"
      :deptId1="info1DeptId"
      @close="show1 = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>

    <!-- 断点监测弹窗 -->
    <el-dialog
      title="断点监测"
      :visible.sync="breakpointDialogVisible"
      width="900px"
      :before-close="handleCloseBreakpointDialog"
    >
      <el-table
        :data="breakpointMonitorData"
        border
        v-loading="breakpointLoading"
        element-loading-text="加载中..."
      >
        <el-table-column prop="monitorIp" label="监测IP" align="center" />
        <el-table-column prop="monitorTime" label="监测时间" align="center" />
        <el-table-column prop="ipStatus" label="IP状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.ipStatus)">
              {{ getStatusText(scope.row.ipStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center">
        <el-pagination
          @current-change="handleBreakpointPageChange"
          @size-change="handleBreakpointSizeChange"
          :current-page="breakpointQueryParams.pageNum"
          :page-size="breakpointQueryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="breakpointTotal"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="breakpointDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listYyJc, hideGjHl } from "@/api/warning/warningList";
import addOrderDialog from "./addOrderDialog.vue";
import infoDialog from "./yyjkDialog.vue";
import { listBreakpointMonitor } from "@/api/monitor/breakpointMonitor";

export default {
  components: { addOrderDialog, infoDialog },
  props: {
    yyId: {
      type: Number,
      default: null,
    },
    deptId: {
      type: Number,
      default: null,
    },
    dateArr: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      datalist: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: "",
      },
      statusOptions: [
        { label: "处理中", value: 1 },
        { label: "已关闭", value: 2 },
      ],
      //查看
      show: false,
      info: {},
      //新增工单
      show1: false,
      info1: {},
      info1DeptName: "",
      info1DeptId: null,
      // 断点监测弹窗相关数据
      breakpointDialogVisible: false,
      applicationId: null,
      breakpointMonitorData: [],
      breakpointLoading: false,
      breakpointTotal: 0,
      breakpointQueryParams: {
        pageNum: 1,
        pageSize: 10,
        dataId: null, // 应用ID，用于查询该应用的断点监测数据
        type: 1,
      },
    };
  },
  watch: {
    yyId(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
    dateArr(newVal, oldVal) {
      this.getList();
    },
  },
  mounted() {
    // this.getList();
  },
  methods: {
    // 显示断点监测弹窗
    showBreakpointDialog(row) {
      this.applicationId = row.yyId;
      this.loadBreakpointMonitorData();
      this.breakpointDialogVisible = true;
    },

    // 加载断点监测数据
    async loadBreakpointMonitorData() {
      try {
        this.breakpointLoading = true;

        // 设置应用ID查询参数
        this.breakpointQueryParams.dataId = this.applicationId;

        console.log("查询断点监测数据，参数:", this.breakpointQueryParams);

        const response = await listBreakpointMonitor(
          this.breakpointQueryParams
        );
        console.log("断点监测API响应:", response);

        if (response.code === 200 && response.data) {
          // 处理返回的数据
          this.breakpointMonitorData = (response.data.list || []).map(
            (item) => ({
              ...item,
              // 格式化监测时间
              monitorTime: item.cTime,
              // 格式化监测IP
              monitorIp: item.monitorIp || item.jcIp || item.ip,
              // 格式化IP状态
              ipStatus: item.ipStatus || item.status,
              // 响应时间
              responseTime: item.responseTime || item.xyTime,
              // 错误信息
              errorMsg: item.errorMsg || item.errorMessage || item.cwxx,
            })
          );

          this.breakpointTotal = response.data.total || 0;
          console.log("断点监测数据加载完成:", this.breakpointMonitorData);
        } else {
          console.error("获取断点监测数据失败:", response.msg);
          this.breakpointMonitorData = [];
          this.breakpointTotal = 0;
          this.$message.error(response.msg || "获取断点监测数据失败");
        }
      } catch (error) {
        console.error("加载断点监测数据异常:", error);
        this.breakpointMonitorData = [];
        this.breakpointTotal = 0;
        this.$message.error("加载断点监测数据失败");
      } finally {
        this.breakpointLoading = false;
      }
    },

    // 关闭断点监测弹窗
    handleCloseBreakpointDialog() {
      this.breakpointDialogVisible = false;
      this.breakpointMonitorData = [];
      this.breakpointTotal = 0;
      // 重置分页参数
      this.breakpointQueryParams.pageNum = 1;
      this.breakpointQueryParams.pageSize = 10;
    },

    // 断点监测分页 - 当前页变化
    handleBreakpointPageChange(page) {
      this.breakpointQueryParams.pageNum = page;
      this.loadBreakpointMonitorData();
    },

    // 断点监测分页 - 每页条数变化
    handleBreakpointSizeChange(size) {
      this.breakpointQueryParams.pageSize = size;
      this.breakpointQueryParams.pageNum = 1;
      this.loadBreakpointMonitorData();
    },
    // 获取状态类型（用于el-tag的type属性）
    getStatusType(status) {
      if (!status) return "info";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "success";
      } else if (
        statusStr === "0" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "danger";
      }
      return "info";
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return "未知";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "正常";
      } else if (
        statusStr === "2" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "异常";
      }
      return status.toString();
    },

    //新增工单开始--
    showDialog(row) {
      this.show1 = true;
      this.info1 = {
        gdType: "3",
        title: row.yyName + "监测异常",
        describe: row.yyName + "于" + row.gjsj + "监测到应用异常，请及时处理。",
        priority: 3,
        handlerId: "",
        yyId: row.yyId,
        dataId: row.gjId,
        dataType: 5,
        csList: [],
      };
      this.info1DeptName = row.deptName;
      this.info1DeptId = row.deptId;
    },
    addSuccess() {
      this.show1 = false;
      this.getList();
    },
    //新增工单结束--
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        this.datalist = [];
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          status: this.queryParams.status,
          yyId: this.yyId,
          constructionUnitId: this.deptId,
        };
        if (this.dateArr && this.dateArr.length > 0) {
          params.startTime = this.dateArr[0];
          params.endTime = this.dateArr[1];
        } else {
          params.startTime = null;
          params.endTime = null;
        }
        const response = await listYyJc(params);
        if (response.code === 200 && response.data) {
          this.datalist = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.datalist = [];
          this.total = 0;
          this.$message.error(response.msg || "获取应用监测失败");
        }
      } catch (error) {
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取应用监测失败");
      }
    },
    goDetail(row) {
      this.info = row;
      this.show = true;
    },
    //忽略
    goHide(row) {
      hideGjHl({ id: row.gjId, type: 5 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("忽略成功");
          this.handleQuery();
        } else {
          this.$message.error(res.msg || "忽略失败");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
</style>
