<template>
  <div class="container">
    <div class="timeSelector flex-c">
      <div>统计日期</div>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="mini"
      >
      </el-date-picker>
    </div>
    <div class="card">
      <gjzl :list="topList"></gjzl>
    </div>
    <div class="flex-s">
      <div class="left">
        <div class="card">
          <div class="cardTitle">安全告警数量趋势</div>
          <aqgj :data="aqgjData"></aqgj>
        </div>
        <div class="card">
          <div class="cardTitle">应用告警数量趋势</div>
          <yygj :data="yygjData"></yygj>
        </div>
        <div class="card">
          <div class="cardTitle">云资源告警数量趋势</div>
          <yzygj :data="yzygjData"></yzygj>
        </div>
      </div>
      <div class="center">
        <div class="card">
          <div class="cardTitle">拦截攻击类型分析</div>
          <ljgj :data="ljgjData"></ljgj>
        </div>
        <div class="card">
          <div class="cardTitle">高频事件类型</div>
          <gpsj :data="gpsjData"></gpsj>
        </div>
      </div>
      <div class="right">
        <div class="card">
          <div class="cardTitle">告警单位排名</div>
          <gjdw :data="gjdwData"></gjdw>
        </div>
        <div class="card">
          <div class="cardTitle">告警处置时长</div>
          <gjcz :data="gjczData"></gjcz>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import gjzl from "@/views/warning/warningOverview/components/gjzl.vue";
import aqgj from "@/views/warning/warningOverview/components/aqgj.vue";
import yygj from "@/views/warning/warningOverview/components/yygj.vue";
import yzygj from "@/views/warning/warningOverview/components/yzygj.vue";
import ljgj from "@/views/warning/warningOverview/components/ljgj.vue";
import gpsj from "@/views/warning/warningOverview/components/gpsj.vue";
import gjdw from "@/views/warning/warningOverview/components/gjdw.vue";
import gjcz from "@/views/warning/warningOverview/components/gjcz.vue";
export default {
  components: { gjzl, aqgj, yygj, yzygj, ljgj, gpsj, gjdw, gjcz },
  data() {
    return {
      dateRange: [],
      topList: [
        {
          name: "产生告警",
          value: "101.56",
          unit: "万",
          icon: require("@/assets/images/warning/csgj.png"),
          list: [
            { label: "接口风险", value: "38.25万" },
            { label: "服务风险", value: "61.31万" },
          ],
        },
        {
          name: "风险隐患",
          value: "101",
          unit: "个",
          icon: require("@/assets/images/warning/fxyh.png"),
          list: [
            { label: "低危", value: "1" },
            { label: "中危", value: "0" },
            { label: "高危", value: "100" },
          ],
        },
        {
          name: "安全事件",
          value: "0",
          unit: "个",
          icon: require("@/assets/images/warning/aqsj.png"),
          list: [
            { label: "低风险", value: "1" },
            { label: "中风险", value: "0" },
            { label: "高风险", value: "0" },
          ],
        },
      ],
      aqgjData: [
        {
          name: "0601",
          value1: "10",
          value2: "11",
          value3: "21",
          value4: "20",
        },
        { name: "0602", value1: "8", value2: "9", value3: "25", value4: "25" },
        { name: "0603", value1: "7", value2: "11", value3: "3", value4: "0" },
        {
          name: "0604",
          value1: "12",
          value2: "12",
          value3: "20",
          value4: "25",
        },
        {
          name: "0605",
          value1: "10",
          value2: "11",
          value3: "30",
          value4: "20",
        },
      ],
      yygjData: [
        { name: "0601", value: 10 },
        { name: "0602", value: 9 },
        { name: "0603", value: 8 },
        { name: "0604", value: 20 },
        { name: "0605", value: 15 },
        { name: "0606", value: 8 },
      ],
      yzygjData: [
        {
          name: "0601",
          value1: "10",
          value2: "11",
          value3: "21",
          value4: "20",
        },
        { name: "0602", value1: "8", value2: "9", value3: "25", value4: "25" },
        { name: "0603", value1: "7", value2: "11", value3: "3", value4: "0" },
        {
          name: "0604",
          value1: "12",
          value2: "12",
          value3: "20",
          value4: "25",
        },
        {
          name: "0605",
          value1: "10",
          value2: "11",
          value3: "30",
          value4: "20",
        },
      ],
      ljgjData: [
        { name: "web攻击", value: 43 },
        { name: "恶意程序", value: 8 },
        { name: "横向渗透", value: 24 },
        { name: "账号异常", value: 21 },
        { name: "探测扫描", value: 21 },
      ],
      gpsjData: [
        { name: "挖矿", value: 289 },
        { name: "弱口令", value: 214 },
        { name: "远程控制", value: 167 },
        { name: "webshell", value: 150 },
        { name: "勒索病毒", value: 143 },
      ],
      gjdwData: [
        { name: "中共兰溪市委党校", value: 38 },
        { name: "金华经开区国资监", value: 34 },
        { name: "兰溪市柏社乡人民", value: 30 },
        { name: "浦江县档案馆", value: 28 },
        { name: "兰溪市香溪镇人民", value: 20 },
      ],
      gjczData: [
        { name: "2小时以内", value: 43 },
        { name: "2-6小时", value: 8 },
        { name: "6-12小时", value: 24 },
        { name: "12小时以上", value: 21 },
      ],
    };
  },
  mounted() {
    this.getCurrentMonth();
  },
  methods: {
    getCurrentMonth() {
      let dateStart = new Date().setDate(1);
      let month = new Date().getMonth();
      let dateEnd = new Date(new Date().setMonth(month + 1)).setDate(0);
      this.dateRange = [dateStart, dateEnd];
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
  position: relative;
}
.card {
  width: 100%;
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
.left,
.center,
.right {
  flex: 1;
  height: auto;
}
.left,
.center {
  margin-right: 12px;
}
.timeSelector {
  position: absolute;
  right: 20px;
  top: -40px;
  justify-content: flex-end;
  & > div {
    margin-right: 12px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
