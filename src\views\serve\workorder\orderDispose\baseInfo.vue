<template>
  <div class="wrap">
    <div class="line3">
      <div class="item">
        <div class="label">工单类型</div>
        <div class="value">{{ formatGdType(data.gdType) }}</div>
      </div>
      <div class="item">
        <div class="label">创建时间</div>
        <div class="value">{{ data.cTime || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">创建人</div>
        <div class="value">{{ data.createName || "-" }}</div>
      </div>
    </div>
    <div class="line3">
      <div class="item">
        <div class="label">处理人</div>
        <div class="value">{{ data.handlerName || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">优先级</div>
        <div
          class="tag flex-c-c"
          v-if="data.priority"
          :class="
            data.priority == 3
              ? 'tag_red'
              : data.priority == 2
              ? 'tag_org'
              : 'tag_blue'
          "
        >
          {{ formatPriority(data.priority) }}
        </div>
        <div v-else>-</div>
      </div>
      <div class="item">
        <div class="label">所属系统</div>
        <div class="value">{{ data.yyName || "-" }}</div>
      </div>
    </div>
    <div class="line3">
      <div class="item">
        <div class="label">工单来源</div>
        <div class="value">
          {{ data.cjType == 1 ? "手动新增" : "系统新增" }}
        </div>
      </div>
      <div class="item">
        <div class="label">关联事件</div>
        <div class="value text_link" v-if="data.dataId" @click="showDetail">
          {{ data.dataId }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="item">
        <div class="label"></div>
        <div class="value"></div>
      </div>
    </div>
    <div class="line3">
      <div class="item">
        <div class="label">抄送</div>
        <div class="value" v-if="data.csList && data.csList.length > 0">
          <span v-for="(item, index) of data.csList" :key="index"
            >{{ item.nickName }}（{{ item.status == 1 ? "未阅" : "已阅" }}）{{
              index != data.csList.length - 1 ? "、 " : ""
            }}</span
          >
        </div>
        <div v-else>-</div>
      </div>
    </div>
    <!-- <div class="line3">
      <div class="item">
        <div class="label">关联资源</div>
        <div class="value">{{ data.glzy || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">关联告警</div>
        <div class="value">{{ data.glgj || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">SLA</div>
        <div class="value">{{ data.sla || "-" }}</div>
      </div>
    </div> -->

    <!-- 应用监测详情弹窗 -->
    <yyjkDialog
      v-if="data.dataType == 5"
      :show="show"
      :info="info"
      @close="show = false"
    ></yyjkDialog>
    <!-- 基础设施告警详情弹窗 -->
    <qtjkDialog
      v-if="data.dataType == 6"
      :show="show"
      :info="info"
      @close="show = false"
    ></qtjkDialog>
    <!-- 安全隐患预警详情弹窗 -->
    <aqyhDialog
      v-if="data.dataType == 1"
      :show="show"
      :info="info"
      @close="show = false"
    ></aqyhDialog>
    <!-- 云资源预警详情弹窗 -->
    <yzyDialog
      v-if="data.dataType == 2"
      :show="show"
      :info="info"
      @close="show = false"
    ></yzyDialog>
    <!-- 数据库预警详情弹窗 -->
    <sjkDialog
      v-if="data.dataType == 3"
      :show="show"
      :info="info"
      @close="show = false"
    ></sjkDialog>
  </div>
</template>

<script>
import aqyhDialog from "@/views/warning/warningList/components/aqyhDialog.vue";
import sjkDialog from "@/views/warning/warningList/components/sjkDialog.vue";
import yyjkDialog from "@/views/warning/warningList/components/yyjkDialog.vue";
import qtjkDialog from "@/views/warning/warningList/components/qtjkDialog.vue";
import yzyDialog from "@/views/warning/warningList/components/yzyDialog.vue";
import { getGjInfo } from "@/api/warning/warningList";

export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    aqyhDialog,
    sjkDialog,
    yyjkDialog,
    yzyDialog,
    qtjkDialog,
  },

  dicts: ["gdlx"],
  data() {
    return {
      priorityOptions: [
        { label: "低", value: 1 },
        { label: "中", value: 2 },
        { label: "高", value: 3 },
      ],
      //查看
      show: false,
      info: {},
    };
  },
  methods: {
    showDetail() {
      getGjInfo({ id: this.data.dataId, type: this.data.dataType }).then(
        (res) => {
          this.info = res.data;
          this.show = true;
        }
      );
    },
    formatGdType(obj) {
      if (this.dict.type.gdlx.find((item) => item.value == obj)) {
        return this.dict.type.gdlx.find((item) => item.value == obj).label;
      }
    },
    formatPriority(obj) {
      return this.priorityOptions.find((item) => item.value == obj).label;
    },
  },
};
</script>

<style lang="scss" scoped>
.line3 {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  box-sizing: border-box;
}
.item {
  flex: 1;
  display: flex;
  margin-right: 20px;
  .label {
    width: 80px;
    margin-right: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
  }
  .value {
    width: auto;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
  }
  .tag {
    width: 28px;
    height: 20px;
    border-radius: 6px 6px 6px 6px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
  }
  .tag_red {
    background-color: #ff324b;
  }
  .tag_org {
    background-color: #f98f1c;
  }
  .tag_blue {
    background-color: #36d1ff;
  }
}
</style>
