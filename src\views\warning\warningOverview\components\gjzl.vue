<template>
  <div class="wrap">
    <div class="itemList flex-b">
      <div class="item" v-for="(item, i) in list" :key="i">
        <div class="flex-b">
          <div>
            <div class="name">{{ item.name }}</div>
            <div class="num">
              {{ item.value }} <span class="unit">{{ item.unit }}</span>
            </div>
          </div>
          <img :src="item.icon" class="icon" />
        </div>
        <div class="list flex-c">
          <div class="li" v-for="(x, j) in item.list" :key="j">
            {{ x.label }}：{{ x.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.itemList {
  align-items: stretch;
}
.item {
  width: calc((100% - 21 * 2px) / 3);
  margin-right: 21px;
  padding: 26px 30px 12px 30px;
  box-sizing: border-box;
  border-radius: 16px;
  &:last-child {
    margin-right: 0;
  }
  &:nth-child(1) {
    background: linear-gradient(180deg, #fff8f1 0%, #ffefe7 100%);
  }
  &:nth-child(2) {
    background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
  }
  &:nth-child(3) {
    background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
  }
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 18px;
    color: #4e5969;
    line-height: 28px;
    text-align: left;
    margin-bottom: 10px;
  }
  .num {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30px;
    color: #1d2129;
    line-height: 34px;
    text-align: left;
    .unit {
      font-size: 24px;
    }
  }
  .icon {
    width: 80px;
    height:80px;
  }
  .list {
    margin-top: 22px;
    .li {
      margin-right: 24px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #667085;
      line-height: 24px;
      text-align: left;
    }
  }
}
</style>
