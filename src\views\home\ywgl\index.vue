<template>
  <div>
    <div class="flex-b" style="align-items: stretch; height: 100%">
      <div class="yzy">
        <div class="subtitle">云资源</div>
        <div class="yzyList flex-c">
          <div
            class="yzyItem"
            v-for="(item, i) in yzyList"
            :key="i"
            @click="showDialog(i)"
            :style="{ cursor: i == 2 ? 'pointer' : '' }"
          >
            <div class="flex-c">
              <div class="name">{{ item.name }}</div>
              <el-select
                v-if="i == 0"
                v-model="yzyValue1"
                style="width: 80px"
                size="mini"
              >
                <el-option
                  v-for="(x, j) in serverOptions"
                  :key="j"
                  :label="x.label"
                  :value="x.value"
                ></el-option>
              </el-select>
              <el-select
                v-if="i == 2"
                v-model="yzyValue2"
                style="width: 80px"
                size="mini"
              >
                <el-option
                  v-for="(x, j) in timeOptions"
                  :key="j"
                  :label="x.label"
                  :value="x.value"
                ></el-option>
              </el-select>
            </div>
            <div class="itemValue">
              <div v-if="i == 1" class="flex-c">
                <div class="icon"></div>
                <div class="value">
                  {{ parseFloat(item.value).toFixed(1) }}
                  <span class="unit">{{ item.unit }}</span>
                </div>
              </div>
              <div v-else-if="i == 2" class="flex-c">
                <div class="value" style="">
                  <span class="unit">CPU:</span>
                  {{ parseFloat(item.value1).toFixed(1) }}
                  <span class="unit">{{ item.unit }}</span>
                </div>
                <div class="value" style="margin-left: 10px">
                  <span class="unit">内存:</span>
                  {{ parseFloat(item.value2).toFixed(1) }}
                  <span class="unit">{{ item.unit }}</span>
                </div>
              </div>
              <div class="flex-c" v-else>
                <div class="value">
                  {{ item.value }}
                  <span class="unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="aq">
        <div class="subtitle">安全</div>
        <div id="lineChart" style="width: 100%; height: 230px"></div>
      </div>
      <div class="alarm">
        <div class="alarmTable">
          <div class="alarmTitle">未完成预警</div>
          <div class="flex-b alarm_item red_bkg">
            <div class="text_red">特别紧急</div>
            <div class="num">{{ alarmData.tbjj }}</div>
          </div>
          <div class="flex-b alarm_item orange_bkg">
            <div class="text_orange">紧急</div>
            <div class="num">{{ alarmData.jj }}</div>
          </div>
          <div class="flex-b alarm_item yellow_bkg">
            <div class="text_yellow">重要</div>
            <div class="num">{{ alarmData.zy }}</div>
          </div>
          <div class="flex-b alarm_item blue_bkg">
            <div class="text_blue">一般</div>
            <div class="num">{{ alarmData.yb }}</div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="50%"
      show-close
    >
      <el-table :data="tablelist">
        <!-- <el-table-column type="index"> </el-table-column> -->
        <el-table-column prop="id" label="序号" align="center">
        </el-table-column>
        <el-table-column prop="yymc" label="应用名称"></el-table-column>
        <el-table-column prop="jsdw" label="建设单位"></el-table-column>
        <el-table-column prop="zylyl" label="资源利用率"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getGjqs, getWwcgj } from "@/api/home/<USER>";

export default {
  data() {
    return {
      //云资源
      yzyList: [
        { name: "服务器总数", value: "6000", unit: "台" },
        { name: "应用在线数", value: "98.6", unit: "%" },
        { name: "资源利用率", value1: "68", value2: "82", unit: "%" },
        { name: "网络流量", value: "1.2/5", unit: "Gbps" },
      ],
      yzyValue1: "",
      yzyValue2: "",
      serverOptions: [
        { label: "全部", value: "" },
        { label: "ECS", value: "ECS" },
        { label: "RDS", value: "RDS" },
        { label: "SLB", value: "SLB" },
        { label: "OSS", value: "OSS" },
      ],
      timeOptions: [
        { label: "实时", value: "" },
        { label: "本周", value: "本周" },
        { label: "本月", value: "本月" },
        { label: "本年", value: "本年" },
      ],
      //安全
      lineChartData: [],
      //告警
      alarmData: {},
      //弹窗
      title: "资源利用率",
      dialogVisible: false,
      tablelist: [
        { id: "01", yymc: "应用名称", jsdw: "建设单位", zylyl: "资源利用率" },
        { id: "01", yymc: "应用名称", jsdw: "建设单位", zylyl: "资源利用率" },
        { id: "01", yymc: "应用名称", jsdw: "建设单位", zylyl: "资源利用率" },
      ],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      getWwcgj().then((res) => {
        this.alarmData = res.data;
      });
      getGjqs().then((res) => {
        // 处理接口返回的数据 res.data
        // 将数据转换为图表所需的格式
        this.lineChartData = this.processChartData(res.data);
        this.initChart(this.lineChartData);
      });
    },
    // 处理图表数据的方法
    processChartData(data) {
      if (!data || !Array.isArray(data)) {
        return [];
      }

      return data.map((item) => ({
        label: item.name, // xAxis的data对应字段name
        value1: item.yyjc || 0, // 应用检测对应字段yyjc
        value2: item.ah || 0, // 安全隐患预警对应字段ah
        value3: item.dx || 0, // 云资源预警对应字段dx
        value4: item.mc || 0, // 数据库预警对应字段mc
      }));
    },
    showDialog(i) {
      if (i == 2) {
        this.dialogVisible = true;
      }
    },
    initChart(data) {
      let chart = echarts.init(document.getElementById("lineChart"));
      let option = {
        color: ["#F98E1B", "#3AA1FF"],
        title: {
          text: "告警趋势",
          top: "0",
          left: "0",
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
          },
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: 0,
          top: "28%",
          containLabel: true,
        },
        legend: {
          icon: "square",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          right: 0,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.label),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "应用检测",
            type: "line",
            showSymbol: false,
            color: "#3AA1FF",
            data: data.map((x) => x.value1),
          },
          {
            name: "安全隐患预警",
            type: "line",
            showSymbol: false,
            color: "#F98E1B",
            data: data.map((x) => x.value2),
          },
          {
            name: "云资源预警",
            type: "line",
            showSymbol: false,
            color: "#36CB6D",
            data: data.map((x) => x.value3),
          },
          {
            name: "数据库预警",
            type: "line",
            showSymbol: false,
            color: "#9900FF",
            data: data.map((x) => x.value4),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.cardTitle2 {
  color: #667085;
  font-weight: 500;
}

.yzy {
  width: 45%;
  margin-right: 20px;
  .yzyList {
    flex-wrap: wrap;
    .yzyItem {
      width: calc((100% - 10px) / 2);
      padding: 12px 16px;
      box-sizing: border-box;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
      margin-top: 11px;
      .name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 16px;
        color: #1d2129;
        line-height: 28px;
        margin-right: 14px;
      }
      &:nth-child(2n + 2) {
        margin-right: 0;
      }
      &:first-child {
        background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
      }
      &:nth-child(2) {
        background: linear-gradient(180deg, #fff8f1 0%, #ffefe7 100%);
      }
      &:nth-child(3) {
        background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
      }
      &:nth-child(4) {
        background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
      }
      ::v-deep .el-input__inner {
        border: unset !important;
      }
      .itemValue {
        margin-top: 10px;
        .icon {
          width: 10px;
          height: 10px;
          background: #36cbcb;
          border-radius: 5px 5px 5px 5px;
          margin-right: 8px;
        }
        .value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 22px;
          color: #1d2129;
          line-height: 42px;
        }
        .unit {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 16px;
          color: #1d2129;
          line-height: 42px;
        }
      }
    }
  }
}
.aq {
  width: 45%;
  margin-right: 20px;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.alarm {
  width: 10%;
  height: auto;
  .alarmTable {
    margin-top: 24px;
    .alarmTitle {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
    }
    .alarm_item {
      margin-top: 14px;
      width: 140px;
      height: 36px;
      border-radius: 5px 5px 5px 5px;
      padding: 0 10px;
      box-sizing: border-box;
      .text_red {
        font-size: 14px;
        color: #d9001b;
        line-height: 21px;
      }
      .text_orange {
        font-size: 14px;
        color: #f98f1c;
        line-height: 21px;
      }
      .text_yellow {
        font-size: 14px;
        color: #ffce09;
        line-height: 21px;
      }
      .text_blue {
        font-size: 14px;
        color: #36d1ff;
        line-height: 21px;
      }
      .num {
        font-weight: 400;
        font-size: 16px;
        color: #1d2129;
        line-height: 24px;
      }
      &.red_bkg {
        background: rgba(217, 0, 27, 0.1);
      }
      &.orange_bkg {
        background: rgba(255, 125, 0, 0.1);
      }
      &.yellow_bkg {
        background: rgba(255, 202, 58, 0.1);
      }
      &.blue_bkg {
        background: rgba(54, 209, 255, 0.1);
      }
    }
  }
}
.subtitle {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 18px;
  color: #1d2129;
  line-height: 24px;
  text-align: left;
  padding-left: 10px;
  box-sizing: border-box;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 6px;
    width: 3px;
    height: 13px;
    background-color: #0057fe;
    border-radius: 3px;
  }
}
</style>
