<template>
  <div style="width: 100%; height: 120px" id="yzygjslqs"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    if (this.data) {
      this.initChart(this.data);
    }
  },
  methods: {
    initChart(data) {
      let chart = echarts.init(document.getElementById("yzygjslqs"));
      let option = {
        color: ["#FF3A3A", "#F98E1B", "#FFCA3A", "#3AA1FF"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "0",
          right: 0,
          bottom: "0%",
          top: "24%",
          containLabel: true,
        },
        legend: {
          icon: "rect",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          right: 0,
          top: 0,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "数量",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "平台水位",
            type: "line",
            smooth: false,
            showSymbol: false,
            data: data.map((x) => x.value1),
          },
          {
            name: "实例水位",
            type: "line",
            smooth: false,
            showSymbol: false,
            data: data.map((x) => x.value2),
          },
          {
            name: "资源优化",
            type: "line",
            smooth: false,
            showSymbol: false,
            data: data.map((x) => x.value3),
          },
          {
            name: "使用率告罄",
            type: "line",
            smooth: false,
            showSymbol: false,
            data: data.map((x) => x.value4),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
